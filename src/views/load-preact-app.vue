<template>
	<div>
		<h2>Preact Micro App</h2>
		<div id="preact-app-container" ref="preactContainer"></div>
	</div>
</template>

<script setup>
	import { onMounted, onUnmounted, ref } from 'vue'
	import { mountRootParcel } from 'single-spa'

	const preactContainer = ref(null)
	let parcelInstance = null

	const loadPreactApp = async () => {
		try {
			// 动态导入Preact应用的single-spa配置
			const preactApp = await import('microapps/preact-app/src/singleSpaApp.js')

			// 使用mountRootParcel挂载Preact应用
			parcelInstance = mountRootParcel(preactApp, {
				domElement: preactContainer.value,
				// 传递props给子应用
				customProps: {
					timestamp: Date.now()
				}
			})

			console.log('Preact app mounted successfully')
		} catch (error) {
			console.error('Failed to load Preact app:', error)
		}
	}

	onMounted(() => {
		loadPreactApp()
	})

	onUnmounted(() => {
		// 清理资源
		if (parcelInstance) {
			parcelInstance.unmount()
				.then(() => {
					console.log('Preact app unmounted successfully')
				})
				.catch(error => {
					console.error('Failed to unmount Preact app:', error)
				})
		}
	})
</script>

<style scoped>
#preact-app-container {
	min-height: 400px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 20px;
	margin-top: 20px;
}

h2 {
	color: #333;
	margin-bottom: 20px;
}
</style>
