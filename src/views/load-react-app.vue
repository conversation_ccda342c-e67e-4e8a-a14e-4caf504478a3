<template>
	<div>
		<h2>React Micro App</h2>
		<div id="react-app" ref="reactContainer"></div>
	</div>
</template>

<script setup>
	import { onMounted, onUnmounted, ref } from 'vue'
	import { mountRootParcel } from 'single-spa'

	const reactContainer = ref(null)
	let parcelInstance = null

	const loadReactApp = async () => {
		try {
			// 通过SystemJS动态导入React应用
			// eslint-disable-next-line no-undef
			const reactApp = await System.import("@single-spa/react-app")

			// 使用mountRootParcel挂载React应用
			parcelInstance = mountRootParcel(reactApp, {
				domElement: reactContainer.value,
				// 传递props给子应用
				customProps: {
					message: 'Hello from Vue3 main app!',
					timestamp: Date.now()
				}
			})

			console.log('React app mounted successfully')
		} catch (error) {
			console.error('Failed to load React app:', error)
		}
	}

	onMounted(() => {
		loadReactApp()
	})

	onUnmounted(() => {
		// 清理资源
		if (parcelInstance) {
			parcelInstance.unmount()
				.then(() => {
					console.log('React app unmounted successfully')
				})
				.catch(error => {
					console.error('Failed to unmount React app:', error)
				})
		}
	})
</script>

<style scoped>
#react-app {
	min-height: 400px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 20px;
	margin-top: 20px;
}

h2 {
	color: #333;
	margin-bottom: 20px;
}
</style>