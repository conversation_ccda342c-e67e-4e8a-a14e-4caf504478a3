<template>
	<div>
		<h2>AngularJS Micro App</h2>
		<div id="angularjs-app-container" ref="angularjsContainer"></div>
	</div>
</template>

<script setup>
	import { onMounted, onUnmounted, ref } from 'vue'
	import { mountRootParcel } from 'single-spa'

	const angularjsContainer = ref(null)
	let parcelInstance = null

	const loadAngularJSApp = async () => {
		try {
			// 动态导入AngularJS应用的single-spa配置
			const angularjsApp = await import('microapps/angularjs-app/src/singleSpaApp.js')

			// 使用mountRootParcel挂载AngularJS应用
			parcelInstance = mountRootParcel(angularjsApp, {
				domElement: angularjsContainer.value,
				// 传递props给子应用
				customProps: {
					message: 'Hello from Vue3 main app!',
					timestamp: Date.now()
				}
			})

			console.log('AngularJS app mounted successfully')
		} catch (error) {
			console.error('Failed to load AngularJS app:', error)
		}
	}

	onMounted(() => {
		loadAngularJSApp()
	})

	onUnmounted(() => {
		// 清理资源
		if (parcelInstance) {
			parcelInstance.unmount()
				.then(() => {
					console.log('AngularJS app unmounted successfully')
				})
				.catch(error => {
					console.error('Failed to unmount AngularJS app:', error)
				})
		}
	})
</script>

<style scoped>
#angularjs-app-container {
	min-height: 400px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 20px;
	margin-top: 20px;
}

h2 {
	color: #333;
	margin-bottom: 20px;
}
</style>
