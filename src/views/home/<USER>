<template>
	<div class="home-container">
		<h1>微前端示例项目</h1>
		<div class="description">
			<p>这是一个基于 Single-SPA 的微前端示例项目，展示了如何在 Vue3 主应用中加载不同框架的子应用。</p>

			<h3>支持的微前端框架：</h3>
			<ul class="framework-list">
				<li><strong>Vue3 应用</strong> - 使用 Composition API 的 Vue3 子应用</li>
				<li><strong>Vue2 应用</strong> - 传统的 Vue2 子应用</li>
				<li><strong>React 应用</strong> - 基于 React 18 的子应用</li>
				<li><strong>Preact 应用</strong> - 轻量级的 Preact 子应用</li>
				<!-- <li><strong>Svelte 应用</strong> - 现代的 Svelte 子应用</li> -->
				<li><strong>AngularJS 应用</strong> - 基于 Angular 的子应用</li>
			</ul>

			<h3>加载方式：</h3>
			<ul class="loading-methods">
				<li><strong>Parcel 方式</strong> - 使用 mountRootParcel 动态加载子应用</li>
				<li><strong>SystemJS 方式</strong> - 通过 SystemJS 模块加载系统加载</li>
				<li><strong>直接导入</strong> - 直接导入子应用的 single-spa 配置</li>
			</ul>

			<p class="instruction">点击上方的卡片来加载不同的微前端应用！</p>
		</div>
	</div>
</template>

<script setup>

</script>

<style scoped>
.home-container {
	padding: 20px;
	max-width: 800px;
	margin: 0 auto;
	text-align: left;
}

h1 {
	color: #2c3e50;
	text-align: center;
	margin-bottom: 30px;
}

.description {
	background: #f8f9fa;
	padding: 20px;
	border-radius: 8px;
	border-left: 4px solid #409eff;
}

.framework-list, .loading-methods {
	margin: 15px 0;
	padding-left: 20px;
}

.framework-list li, .loading-methods li {
	margin: 8px 0;
	line-height: 1.6;
}

.instruction {
	margin-top: 20px;
	padding: 15px;
	background: #e6f7ff;
	border: 1px solid #91d5ff;
	border-radius: 4px;
	text-align: center;
	font-weight: 500;
	color: #1890ff;
}

h3 {
	color: #409eff;
	margin: 20px 0 10px 0;
}
</style>
	