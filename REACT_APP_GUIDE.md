# React 微前端应用加载指南

## 🚀 快速启动

### 1. 启动React应用

React应用需要单独启动才能被主应用通过SystemJS加载：

```bash
# 进入React应用目录
cd microapps/react-app

# 安装依赖（如果还没安装）
npm install

# 启动开发服务器
npm start
```

React应用将在 `http://localhost:8080` 启动，并暴露SystemJS模块在：
`http://localhost:8080/react-app-react-app.js`

### 2. 启动主应用

在另一个终端窗口中：

```bash
# 回到项目根目录
cd ../..

# 启动主应用
npm run start
```

主应用将在 `http://localhost:8000` 启动。

### 3. 测试React应用加载

1. 打开浏览器访问 `http://localhost:8000`
2. 点击 "React App" 卡片
3. 应该能看到React应用成功加载，包含：
   - 交互式计数器
   - 实时时钟
   - 从主应用传递的props信息

## 🔧 技术实现

### SystemJS配置

在 `public/index.html` 中配置了SystemJS importmap：

```javascript
{
  "imports": {
    "@react-app/react-app": "http://localhost:8080/react-app-react-app.js",
    "react": "https://cdn.jsdelivr.net/npm/react@17.0.2/umd/react.development.js",
    "react-dom": "https://cdn.jsdelivr.net/npm/react-dom@17.0.2/umd/react-dom.development.js"
  }
}
```

### 加载方式

主应用通过以下方式加载React应用：

```javascript
// 在 src/views/load-react-app.vue 中
const reactApp = await System.import("@react-app/react-app")
parcelInstance = mountRootParcel(reactApp, {
  domElement: reactContainer.value,
  customProps: {
    name: 'React App',
    message: 'Hello from Vue3 main app!',
    // ... 其他props
  }
})
```

## 🐛 故障排除

### 问题1: React应用加载失败

**症状**: 点击React App卡片后显示错误信息

**解决方案**:
1. 确保React应用正在运行在端口8080
2. 检查浏览器控制台是否有CORS错误
3. 验证 `http://localhost:8080/react-app-react-app.js` 可以访问

### 问题2: 端口冲突

**症状**: React应用启动失败，提示端口被占用

**解决方案**:
1. 检查端口8080是否被其他应用占用
2. 修改 `microapps/react-app/webpack.config.js` 中的端口配置
3. 同时更新 `public/index.html` 中的SystemJS importmap

### 问题3: 依赖版本冲突

**症状**: React应用运行异常或报错

**解决方案**:
1. 确保React版本一致（当前使用17.0.2）
2. 清除node_modules并重新安装：
   ```bash
   cd microapps/react-app
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📝 开发说明

### 添加新功能

React应用的主要组件在 `microapps/react-app/src/root.component.jsx`，你可以：

1. 添加新的React组件
2. 使用props接收主应用传递的数据
3. 实现与主应用的通信

### 构建生产版本

```bash
cd microapps/react-app
npm run build
```

构建后的文件将输出到 `dist/` 目录。
