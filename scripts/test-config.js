#!/usr/bin/env node

/**
 * 测试微前端配置的脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查微前端配置...\n');

// 检查主应用配置
function checkMainApp() {
  console.log('📦 检查主应用配置:');
  
  // 检查路由配置
  const routerPath = path.join(__dirname, '../src/router/index.js');
  if (fs.existsSync(routerPath)) {
    console.log('  ✅ 路由配置文件存在');
  } else {
    console.log('  ❌ 路由配置文件缺失');
  }
  
  // 检查加载页面
  const viewsPath = path.join(__dirname, '../src/views');
  const requiredViews = [
    'load-vue3-app.vue',
    'load-vue2-app.vue', 
    'load-react-app.vue',
    'load-preact-app.vue',
    'load-svelte-app.vue',
    'load-angularjs-app.vue'
  ];
  
  requiredViews.forEach(view => {
    const viewPath = path.join(viewsPath, view);
    if (fs.existsSync(viewPath)) {
      console.log(`  ✅ ${view} 存在`);
    } else {
      console.log(`  ❌ ${view} 缺失`);
    }
  });
  
  console.log('');
}

// 检查子应用配置
function checkMicroApps() {
  console.log('🔧 检查子应用配置:');
  
  const microAppsPath = path.join(__dirname, '../microapps');
  const apps = [
    { name: 'vue3-app', singleSpaFile: 'src/singleSpaApp.js' },
    { name: 'vue2-app', singleSpaFile: 'src/main.js' },
    { name: 'react-app', singleSpaFile: 'src/react-app-react-app.js' },
    { name: 'preact-app', singleSpaFile: 'src/singleSpaApp.js' },
    { name: 'svelte-app', singleSpaFile: 'src/singleSpaApp.js' },
    { name: 'angularjs-app', singleSpaFile: 'src/singleSpaApp.js' }
  ];
  
  apps.forEach(app => {
    const appPath = path.join(microAppsPath, app.name);
    const packageJsonPath = path.join(appPath, 'package.json');
    const singleSpaPath = path.join(appPath, app.singleSpaFile);
    
    console.log(`\n  📱 ${app.name}:`);
    
    if (fs.existsSync(appPath)) {
      console.log(`    ✅ 应用目录存在`);
    } else {
      console.log(`    ❌ 应用目录缺失`);
      return;
    }
    
    if (fs.existsSync(packageJsonPath)) {
      console.log(`    ✅ package.json 存在`);
    } else {
      console.log(`    ❌ package.json 缺失`);
    }
    
    if (fs.existsSync(singleSpaPath)) {
      console.log(`    ✅ Single-SPA 配置存在`);
    } else {
      console.log(`    ❌ Single-SPA 配置缺失: ${app.singleSpaFile}`);
    }
  });
  
  console.log('');
}

// 检查依赖
function checkDependencies() {
  console.log('📚 检查依赖配置:');
  
  const packageJsonPath = path.join(__dirname, '../package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = [
      'single-spa',
      'single-spa-vue',
      'single-spa-react',
      'single-spa-preact',
      'single-spa-svelte',
      'single-spa-angularjs'
    ];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`  ✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`  ❌ ${dep}: 缺失`);
      }
    });
  }
  
  console.log('');
}

// 检查启动脚本
function checkScripts() {
  console.log('🚀 检查启动脚本:');
  
  const startScriptPath = path.join(__dirname, 'start-all.sh');
  if (fs.existsSync(startScriptPath)) {
    console.log('  ✅ start-all.sh 存在');
  } else {
    console.log('  ❌ start-all.sh 缺失');
  }
  
  console.log('');
}

// 运行所有检查
function runAllChecks() {
  checkMainApp();
  checkMicroApps();
  checkDependencies();
  checkScripts();
  
  console.log('🎉 配置检查完成！');
  console.log('\n💡 提示:');
  console.log('  - 如果有缺失的文件，请检查项目结构');
  console.log('  - 运行 npm run install:all 安装所有依赖');
  console.log('  - 运行 ./scripts/start-all.sh 启动所有应用');
}

// 执行检查
runAllChecks();
