#!/bin/bash

# 启动React应用的脚本

echo "🚀 启动React微前端应用..."

# 检查是否在正确的目录
if [ ! -d "microapps/react-app" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 进入React应用目录
cd microapps/react-app

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动React应用
echo "🌐 启动React应用在端口 8080..."
echo "📋 应用将在以下地址可用:"
echo "   - 开发服务器: http://localhost:8080"
echo "   - SystemJS模块: http://localhost:8080/react-app-react-app.js"
echo ""
echo "按 Ctrl+C 停止服务..."

npm start
