#!/bin/bash

# 启动所有微前端应用的脚本

echo "🚀 启动微前端示例项目..."

# 启动主应用
echo "📦 启动主应用 (Vue3)..."
npm run start &
MAIN_PID=$!

# 等待主应用启动
sleep 5

# 启动Vue2应用
echo "📦 启动Vue2应用..."
cd microapps/vue2-app && npm run serve &
VUE2_PID=$!
cd ../..

# 启动Vue3应用
echo "📦 启动Vue3应用..."
cd microapps/vue3-app && npm run serve &
VUE3_PID=$!
cd ../..

# 启动React应用
echo "📦 启动React应用..."
cd microapps/react-app && npm run start &
REACT_PID=$!
cd ../..

# 启动Preact应用
echo "📦 启动Preact应用..."
cd microapps/preact-app && npm run dev &
PREACT_PID=$!
cd ../..

# 启动Svelte应用
echo "📦 启动Svelte应用..."
cd microapps/svelte-app && npm run dev &
SVELTE_PID=$!
cd ../..

# 启动AngularJS应用
echo "📦 启动AngularJS应用..."
cd microapps/angularjs-app && npm run start &
ANGULAR_PID=$!
cd ../..

echo "✅ 所有应用已启动！"
echo "🌐 主应用地址: http://localhost:8000"
echo "📋 应用端口分配:"
echo "   - 主应用 (Vue3): 8000"
echo "   - Vue2应用: 8002"
echo "   - Vue3应用: 8001"
echo "   - React应用: 8080"
echo "   - Preact应用: 8080 (开发模式)"
echo "   - Svelte应用: 5000"
echo "   - AngularJS应用: 4200"

# 等待用户输入来停止所有服务
echo ""
echo "按 Ctrl+C 停止所有服务..."

# 捕获中断信号并清理进程
trap 'echo "🛑 停止所有服务..."; kill $MAIN_PID $VUE2_PID $VUE3_PID $REACT_PID $PREACT_PID $SVELTE_PID $ANGULAR_PID 2>/dev/null; exit' INT

# 保持脚本运行
wait
