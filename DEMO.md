# 微前端示例演示指南

## 🎯 演示目标

本演示展示了如何使用 Single-SPA 在一个 Vue3 主应用中集成多个不同框架的微前端应用。

## 🚀 快速演示

### 1. 启动项目

```bash
# 安装依赖
npm install
npm run install:all

# 启动所有应用
chmod +x scripts/start-all.sh
./scripts/start-all.sh
```

### 2. 访问演示

打开浏览器访问 `http://localhost:8080`

## 📋 演示步骤

### 步骤 1: 主应用界面
- 查看主应用的导航卡片
- 每个卡片代表一个不同的微前端应用
- 注意主应用使用 Vue3 + Element Plus

### 步骤 2: Vue3 微应用
- 点击 "load vue3 App" 卡片
- 观察 Vue3 子应用的加载过程
- 注意使用 Parcel 方式加载

### 步骤 3: Vue2 微应用  
- 点击 "load vue2 App" 卡片
- 观察 Vue2 子应用通过 SystemJS 加载
- 注意与 Vue3 应用的差异

### 步骤 4: React 微应用
- 点击 "load react App" 卡片  
- 观察 React 应用的加载
- 注意通过 registerApplication 注册的方式

### 步骤 5: Preact 微应用
- 点击 "load preact App" 卡片
- 观察轻量级 Preact 应用的加载
- 注意与 React 的相似性和差异

### 步骤 6: Svelte 微应用
- 点击 "load svelte App" 卡片
- 观察 Svelte 应用的加载
- 注意编译时优化的特性

### 步骤 7: AngularJS 微应用
- 点击 "load angularjs App" 卡片
- 观察 Angular 应用的加载
- 注意企业级框架的特性

## 🔍 技术要点观察

### 1. 加载方式对比
- **Parcel 方式**: Vue3, Preact, Svelte, AngularJS
- **SystemJS 方式**: Vue2
- **registerApplication 方式**: React

### 2. 生命周期管理
- 观察应用的 mount/unmount 过程
- 查看浏览器控制台的日志输出
- 注意内存清理和资源释放

### 3. 样式隔离
- 每个应用的样式完全隔离
- 不会相互影响
- 主应用样式保持独立

### 4. 状态管理
- 各应用状态完全独立
- 可以通过 props 传递数据
- 支持应用间通信

## 🛠️ 开发者工具

### 浏览器控制台
```javascript
// 查看已注册的应用
window.singleSpa.getAppNames()

// 查看应用状态
window.singleSpa.getAppStatus('react-app')

// 手动卸载应用
window.singleSpa.unloadApplication('react-app')
```

### 网络面板
- 观察各应用的资源加载
- 查看 SystemJS 模块加载
- 监控性能指标

## 🎨 自定义扩展

### 添加新的微前端应用
1. 在 `microapps/` 目录创建新应用
2. 配置 single-spa 生命周期
3. 在主应用中添加路由和加载页面
4. 更新 SystemJS importmap (如需要)

### 修改加载方式
- 可以将 Parcel 方式改为 registerApplication
- 可以添加应用预加载
- 可以实现懒加载优化

## 📊 性能监控

### 关键指标
- **首屏加载时间**: 主应用启动速度
- **应用切换时间**: 微应用加载速度  
- **内存使用**: 应用卸载后的内存释放
- **网络请求**: 资源加载优化

### 优化建议
- 使用 CDN 加载公共依赖
- 实现应用预加载
- 优化 bundle 大小
- 使用 HTTP/2 推送

## 🐛 常见问题

### Q: 应用加载失败怎么办？
A: 检查对应的子应用是否正常启动，查看控制台错误信息

### Q: 样式冲突怎么解决？
A: Single-SPA 提供了样式隔离，确保使用 scoped 样式

### Q: 如何实现应用间通信？
A: 可以通过 props、事件总线或状态管理库实现

### Q: 如何调试微前端应用？
A: 使用浏览器开发者工具，每个应用都可以独立调试
