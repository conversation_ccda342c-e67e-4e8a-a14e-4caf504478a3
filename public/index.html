<!DOCTYPE html>
<html lang="">

<head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible"
            content="IE=edge">
      <meta name="viewport"
            content="width=device-width,initial-scale=1.0">
      <link rel="icon"
            href="<%= BASE_URL %>favicon.ico">
      <title>
            <%= htmlWebpackPlugin.options.title
                %>
      </title>
</head>

<body>
      <noscript>
            <strong>We're sorry but <%= htmlWebpackPlugin.options.title
                      %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
      </noscript>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/system.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/extras/amd.js"></script>
      <script type="systemjs-importmap">
          {
            "imports": {
                 "@react-app/react-app": "http://localhost:8080/react-app-react-app.js",
                 "@single-spa/react-app": "http://localhost:8080/react-app-react-app.js",
                  "@single-spa/vue3-app": "http://localhost:8001/js/app.js",
                  "@single-spa/vue2-app": "http://localhost:8002/js/app.js",
                  "react": "https://cdn.jsdelivr.net/npm/react@17.0.2/umd/react.development.js",
                  "react-dom": "https://cdn.jsdelivr.net/npm/react-dom@17.0.2/umd/react-dom.development.js"
            }
          }
       </script>
      <div id="app"></div>
      <!-- <div id="react-app"></div> -->
      <!-- built files will be auto injected -->
</body>

</html>
