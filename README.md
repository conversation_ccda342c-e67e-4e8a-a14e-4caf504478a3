# 微前端示例项目 (Microapp Examples)

## 项目简介

这是一个基于 **Single-SPA** 的微前端示例项目，展示了如何在 Vue3 主应用中加载和管理多个不同框架的子应用。

## 🚀 支持的框架

- **Vue3** - 使用 Composition API 的现代 Vue 应用
- **Vue2** - 传统的 Vue2 应用
- **React** - 基于 React 18 的应用
- **Preact** - 轻量级的 Preact 应用
- **Svelte** - 现代的 Svelte 应用
- **AngularJS** - 基于 Angular 15 的应用

## 📦 项目结构

```
microapp-examples/
├── src/                    # 主应用 (Vue3)
│   ├── views/             # 各微前端加载页面
│   │   ├── load-vue3-app.vue
│   │   ├── load-vue2-app.vue
│   │   ├── load-react-app.vue
│   │   ├── load-preact-app.vue
│   │   ├── load-svelte-app.vue
│   │   └── load-angularjs-app.vue
│   └── router/            # 路由配置
├── microapps/             # 微前端子应用
│   ├── vue3-app/         # Vue3 子应用
│   ├── vue2-app/         # Vue2 子应用
│   ├── react-app/        # React 子应用
│   ├── preact-app/       # Preact 子应用
│   ├── svelte-app/       # Svelte 子应用
│   └── angularjs-app/    # AngularJS 子应用
└── scripts/              # 启动脚本
```

## 🛠️ 技术栈

- **主应用**: Vue3 + Vue Router + Element Plus
- **微前端框架**: Single-SPA
- **模块加载**: SystemJS
- **构建工具**: Webpack, Vite, Rollup (根据不同子应用)

## 📋 快速开始

### 1. 安装依赖

```bash
# 安装主应用依赖
npm install

# 安装所有子应用依赖
npm run install:all
```

### 2. 启动开发环境

#### 方式一：使用启动脚本（推荐）
```bash
chmod +x scripts/start-all.sh
./scripts/start-all.sh
```

#### 方式二：手动启动
```bash
# 启动主应用
npm run start

# 在新终端中启动各个子应用
cd microapps/vue2-app && npm run serve
cd microapps/vue3-app && npm run serve
cd microapps/react-app && npm run start
cd microapps/preact-app && npm run dev
cd microapps/svelte-app && npm run dev
cd microapps/angularjs-app && npm run start
```

### 3. 启动React应用 (重要)

React应用需要单独启动才能被主应用加载：

```bash
# 方法1: 使用提供的脚本
./scripts/start-react-app.sh

# 方法2: 手动启动
cd microapps/react-app
npm install  # 如果还没安装依赖
npm start    # 启动在端口 8080
```

### 4. 访问应用

打开浏览器访问 `http://localhost:8000`，点击不同的卡片来加载各个微前端应用。

**注意**: React应用需要先启动才能正常加载，否则会显示错误信息。

## 🔧 Single-SPA 集成方式

### 1. 通过 registerApplication 注册应用

```javascript
registerApplication(
	'react-app',
	() => System.import("@single-spa/react-app"),
	location => location.hash.startsWith('#/react-app'),
	{
		domElementGetter: function () {
			return document.getElementById('react-app')
		}
	}
)
```

### 2. 使用 mountRootParcel 动态加载

```vue
<template>
	<div id="app-container" ref="container"></div>
</template>

<script setup>
	import { mountRootParcel } from 'single-spa'

	const loadApp = async () => {
		const app = await import('microapps/vue3-app/src/singleSpaApp.js')
		const parcel = mountRootParcel(app, {
			domElement: container.value,
			customProps: { message: 'Hello from main app!' }
		})
	}
</script>
```

### 3. 使用 Parcel 组件

```vue
<template>
	<Parcel
		:config="parcelConfig"
		:mountParcel="mountRootParcel"
		wrapWith="div"
		:parcelProps="getParcelProps()" />
</template>

<script setup>
	import Parcel from 'single-spa-vue/parcel'
	import { mountRootParcel } from 'single-spa'

	const parcelConfig = computed(() =>
		System.import("@single-spa/vue2-app")
	)
</script>
```

## 🏗️ 各子应用配置

### Vue3 应用
- **端口**: 8001
- **构建工具**: Vue CLI
- **Single-SPA配置**: `src/singleSpaApp.js`

### Vue2 应用
- **端口**: 8002
- **构建工具**: Vue CLI
- **Single-SPA配置**: `src/main.js`

### React 应用
- **端口**: 8080 (与主应用共享)
- **构建工具**: Webpack + single-spa-react
- **Single-SPA配置**: `src/react-app-react-app.js`

### Preact 应用
- **端口**: 8080 (与主应用共享)
- **构建工具**: Preact CLI
- **Single-SPA配置**: `src/singleSpaApp.js`

### Svelte 应用
- **端口**: 5000
- **构建工具**: Rollup
- **Single-SPA配置**: `src/singleSpaApp.js`

### AngularJS 应用
- **端口**: 4200
- **构建工具**: Angular CLI
- **Single-SPA配置**: `src/singleSpaApp.js`

## 🔍 功能特性

- ✅ **多框架支持** - 同时运行 6 种不同的前端框架
- ✅ **动态加载** - 按需加载微前端应用
- ✅ **路由集成** - 与主应用路由系统集成
- ✅ **状态隔离** - 各应用状态完全隔离
- ✅ **样式隔离** - CSS 样式不会相互影响
- ✅ **错误边界** - 单个应用错误不影响其他应用
- ✅ **开发友好** - 支持热重载和独立开发

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8080
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   npm install
   ```

3. **子应用加载失败**
   - 检查子应用是否正常启动
   - 确认 SystemJS importmap 配置正确
   - 查看浏览器控制台错误信息

## 📚 学习资源

- [Single-SPA 官方文档](https://single-spa.js.org/)
- [微前端架构指南](https://micro-frontends.org/)
- [Vue3 官方文档](https://vuejs.org/)
- [React 官方文档](https://reactjs.org/)

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Single-SPA](https://single-spa.js.org/) - 微前端框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [React](https://reactjs.org/) - 用户界面库
- [Element Plus](https://element-plus.org/) - Vue3 组件库
