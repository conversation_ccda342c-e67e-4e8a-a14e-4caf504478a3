{"name": "microapp-examples", "version": "0.1.0", "private": true, "scripts": {"start": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "install:all": "npm run install:vue2 && npm run install:vue3 && npm run install:react && npm run install:preact && npm run install:svelte && npm run install:angular", "install:vue2": "cd microapps/vue2-app && npm install", "install:vue3": "cd microapps/vue3-app && npm install", "install:react": "cd microapps/react-app && npm install", "install:preact": "cd microapps/preact-app && npm install", "install:svelte": "cd microapps/svelte-app && npm install", "install:angular": "cd microapps/angularjs-app && npm install", "test:config": "node scripts/test-config.js"}, "dependencies": {"angular": "^1.6.10", "core-js": "^3.8.3", "element-plus": "^2.2.30", "preact": "^10.12.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "single-spa": "^5.9.4", "single-spa-angularjs": "^4.3.1", "single-spa-preact": "^1.1.0", "single-spa-react": "^5.0.0", "single-spa-svelte": "^2.1.1", "single-spa-vue": "^2.5.1", "systemjs": "^6.13.0", "svelte": "^3.55.1", "systemjs-webpack-interop": "^2.3.7", "vue": "^3.2.47", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "lerna": "^6.5.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "workspaces": ["packages/*", "microapps/*"]}