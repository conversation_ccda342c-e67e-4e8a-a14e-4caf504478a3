import singleSpaPreact from "single-spa-preact";
import { h, render } from "preact";
import App from "./components/app";

const preactLifecycles = singleSpaPreact({
	preact: { h, render },
	rootComponent: App,
	errorBoundary(err, _info, _props) {
		// Custom error boundary handling
		console.error("Preact microfrontend error:", err);
		return h("div", { style: { color: "red", padding: "20px" } }, [
			h("h3", null, "Something went wrong in Preact app"),
			h("p", null, err.message)
		]);
	},
});

export const { bootstrap, mount, unmount } = preactLifecycles;
