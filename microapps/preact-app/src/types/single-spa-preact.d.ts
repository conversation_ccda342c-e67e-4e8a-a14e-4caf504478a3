declare module 'single-spa-preact' {
  import { ComponentType } from 'preact';
  
  interface SingleSpaPreactOptions {
    preact: {
      h: any;
      render: any;
    };
    rootComponent: ComponentType<any>;
    errorBoundary?: (err: Error, info: any, props: any) => any;
    domElementGetter?: () => HTMLElement;
  }
  
  interface SingleSpaLifecycles {
    bootstrap: (props: any) => Promise<void>;
    mount: (props: any) => Promise<void>;
    unmount: (props: any) => Promise<void>;
  }
  
  function singleSpaPreact(options: SingleSpaPreactOptions): SingleSpaLifecycles;
  
  export = singleSpaPreact;
}
