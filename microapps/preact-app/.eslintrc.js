module.exports = {
  extends: ['preact'],
  parser: 'espree', // Use espree instead of babel-eslint to avoid decorator conflicts
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  env: {
    browser: true,
    es6: true,
    node: true
  },
  rules: {
    // Disable spellcheck rules that are causing "Unknown word" errors
    'spellcheck/spell-checker': 'off',
    // Allow unused parameters that start with underscore
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }]
  },
  // Add custom words to prevent spellcheck warnings
  settings: {
    'spellcheck/spell-checker': {
      skipWords: ['Lifecycles', 'microfrontend', 'preact']
    }
  },
  ignorePatterns: ['build/', 'node_modules/']
};
