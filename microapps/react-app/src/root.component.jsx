import React, { useState, useEffect } from 'react';

export default function Root(props) {
  const [count, setCount] = useState(0);
  const [currentTime, setCurrentTime] = useState(new Date().toLocaleTimeString());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleIncrement = () => {
    setCount(count + 1);
  };

  const handleDecrement = () => {
    setCount(count - 1);
  };

  return (
    <div style={{
      padding: '20px',
      border: '2px solid #61dafb',
      borderRadius: '8px',
      backgroundColor: '#f0f8ff',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h2 style={{ color: '#61dafb', marginTop: 0 }}>
        我是子应用 🚀 {props.name || 'React App'} is mounted!
      </h2>

      <div style={{ marginBottom: '15px' }}>
        <h3>Props from Main App:</h3>
        <ul>
          <li><strong>Message:</strong> {props.message}</li>
          <li><strong>Timestamp:</strong> {new Date(props.timestamp).toLocaleString()}</li>
          {props.data && (
            <>
              <li><strong>Title:</strong> {props.data.title}</li>
              <li><strong>Description:</strong> {props.data.description}</li>
            </>
          )}
        </ul>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h3>Interactive Counter:</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <button
            onClick={handleDecrement}
            style={{
              padding: '8px 16px',
              backgroundColor: '#ff6b6b',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            -
          </button>
          <span style={{
            fontSize: '24px',
            fontWeight: 'bold',
            minWidth: '40px',
            textAlign: 'center'
          }}>
            {count}
          </span>
          <button
            onClick={handleIncrement}
            style={{
              padding: '8px 16px',
              backgroundColor: '#51cf66',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            +
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h3>Live Clock:</h3>
        <div style={{
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#495057',
          backgroundColor: '#e9ecef',
          padding: '10px',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          {currentTime}
        </div>
      </div>

      <div style={{
        fontSize: '14px',
        color: '#6c757d',
        fontStyle: 'italic'
      }}>
        This React component is running as a micro frontend within the Vue.js main application.
      </div>
    </div>
  );
}
