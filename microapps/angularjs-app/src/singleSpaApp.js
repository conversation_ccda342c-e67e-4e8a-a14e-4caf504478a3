import 'zone.js/dist/zone.js'
import { NgZone } from '@angular/core';
// import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { ɵAnimationEngine as AnimationEngine } from '@angular/animations/browser';

import { singleSpaAngular } from 'single-spa-angular';

// import { AppModule } from './app/app.module';
// import { environment } from './environments/environment';
// import { singleSpaPropsSubject } from './single-spa/single-spa-props';

// if (environment.production) {
// 	enableProdMode();
// }

const lifecycles = singleSpaAngular({
	// bootstrapFunction: singleSpaProps => {
	// 	// singleSpaPropsSubject.next(singleSpaProps);
	// 	return platformBrowserDynamic().bootstrapModule(AppModule);
	// },
	template: '<app-root />',
	NgZone,
	AnimationEngine,
});

export const bootstrap = lifecycles.bootstrap;
export const mount = lifecycles.mount;
export const unmount = lifecycles.unmount;
